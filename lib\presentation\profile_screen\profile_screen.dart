import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sizer/sizer.dart';
import 'package:path/path.dart' as path;

import '../../core/app_export.dart';
import '../../services/auth_service.dart';
import '../../services/user_service.dart';
import './widgets/logout_button_widget.dart';
import './widgets/profile_action_list_widget.dart';
import './widgets/profile_header_widget.dart';
import './widgets/profile_info_card_widget.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final AuthService _authService = AuthService();
  final UserService _userService = UserService();

  bool _isLoading = true;
  Map<String, dynamic> _userData = {};
  Map<String, dynamic> _userStats = {};

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load user profile from Supabase
      final profile = await _userService.getCurrentUserProfile();
      final stats = await _userService.getUserStatistics();

      if (profile != null) {
        setState(() {
          _userData = {
            'id': profile['id'],
            'name': profile['full_name'] ?? 'Unknown User',
            'email': profile['email'] ?? '',
            'phone': profile['phone'] ?? '',
            'region': profile['region'] ?? '',
            'avatar': profile['avatar_url'],
            'joinDate': profile['created_at'],
            'role': profile['role'],
          };
          _userStats = stats;
          _isLoading = false;
        });
      } else {
        // Fallback to shared preferences for offline data
        await _loadOfflineUserData();
      }
    } catch (e) {
      // Fallback to shared preferences if Supabase fails
      await _loadOfflineUserData();
    }
  }

  Future<void> _loadOfflineUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final user = _authService.getCurrentUser();

      setState(() {
        _userData = {
          'id': user?.id ?? 'offline_user',
          'name': prefs.getString('user_name') ??
              user?.userMetadata?['full_name'] ??
              'Unknown User',
          'email': user?.email ?? prefs.getString('user_email') ?? '',
          'phone': prefs.getString('user_phone') ??
              user?.userMetadata?['phone'] ??
              '',
          'region': prefs.getString('user_region') ??
              user?.userMetadata?['region'] ??
              '',
          'avatar': prefs.getString('user_avatar') ??
              user?.userMetadata?['avatar_url'],
          'joinDate': user?.createdAt ?? DateTime.now().toIso8601String(),
          'role': 'member',
        };
        _userStats = {
          'queries_count': 0,
          'groups_count': 0,
          'comments_count': 0,
          'likes_received': 0,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _userData = {
          'id': 'unknown',
          'name': 'Unknown User',
          'email': '',
          'phone': '',
          'region': '',
          'avatar': null,
          'joinDate': DateTime.now().toIso8601String(),
          'role': 'member',
        };
        _userStats = {
          'queries_count': 0,
          'groups_count': 0,
          'comments_count': 0,
          'likes_received': 0,
        };
        _isLoading = false;
      });
    }
  }

  Future<void> _handleLogout() async {
    final shouldLogout = await _showLogoutConfirmation();
    if (shouldLogout == true) {
      await _performLogout();
    }
  }

  Future<bool?> _showLogoutConfirmation() {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Logout',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          content: Text(
            'Are you sure you want to logout? You will need to sign in again to access your account.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.lightTheme.colorScheme.error,
                foregroundColor: Colors.white,
              ),
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _performLogout() async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Sign out from Supabase
      await _authService.signOut();

      // Clear local storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      // Navigate to registration screen and clear stack
      if (mounted) {
        Navigator.of(context).pop(); // Remove loading dialog
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/registration-screen',
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Remove loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Logout failed: ${e.toString()}'),
            backgroundColor: AppTheme.lightTheme.colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _handleAvatarTap() async {
    // Show image picker options
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: CustomIconWidget(
                  iconName: 'camera_alt',
                  color: AppTheme.lightTheme.primaryColor,
                  size: 24,
                ),
                title: const Text('Take Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _updateProfilePhoto('camera');
                },
              ),
              ListTile(
                leading: CustomIconWidget(
                  iconName: 'photo_library',
                  color: AppTheme.lightTheme.primaryColor,
                  size: 24,
                ),
                title: const Text('Choose from Gallery'),
                onTap: () {
                  Navigator.pop(context);
                  _updateProfilePhoto('gallery');
                },
              ),
              ListTile(
                leading: CustomIconWidget(
                  iconName: 'delete',
                  color: AppTheme.lightTheme.colorScheme.error,
                  size: 24,
                ),
                title: const Text('Remove Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _removeProfilePhoto();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _updateProfilePhoto(String source) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        return;
      }

      final file = result.files.first;
      final fileName = path.basename(file.name);

      // Upload to Supabase
      final newAvatarUrl = await _userService.uploadAvatar(file.path!, fileName);

      // Update local storage as backup
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_avatar', newAvatarUrl);

      setState(() {
        _userData['avatar'] = newAvatarUrl;
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Profile photo updated successfully'),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update profile photo: ${e.toString()}'),
          backgroundColor: AppTheme.lightTheme.colorScheme.error,
        ),
      );
    }
  }

  Future<void> _removeProfilePhoto() async {
    try {
      // Update in Supabase
      await _userService.updateUserProfile(avatarUrl: null);

      // Remove from local storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_avatar');

      setState(() {
        _userData['avatar'] = null;
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Profile photo removed'),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to remove profile photo: ${e.toString()}'),
          backgroundColor: AppTheme.lightTheme.colorScheme.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: _isLoading ? _buildLoadingState() : _buildProfileContent(),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Column(
      children: [
        ProfileHeaderWidget(
          onSettingsTap: () {},
        ),
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            child: Column(
              children: [
                SizedBox(height: 3.h),
                // Avatar skeleton
                Container(
                  width: 25.w,
                  height: 25.w,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(height: 2.h),
                // Name skeleton
                Container(
                  width: 40.w,
                  height: 2.h,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                SizedBox(height: 1.h),
                // Phone skeleton
                Container(
                  width: 50.w,
                  height: 1.5.h,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                SizedBox(height: 3.h),
                // Card skeleton
                Container(
                  width: double.infinity,
                  height: 15.h,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileContent() {
    // Combine user data with stats for the info card
    final combinedUserData = {
      ..._userData,
      'totalQueries': _userStats['queries_count'] ?? 0,
      'totalGroups': _userStats['groups_count'] ?? 0,
      'reputation': _userStats['likes_received'] ?? 0,
    };

    return Column(
      children: [
        ProfileHeaderWidget(
          onSettingsTap: () {
            // Navigate to settings screen
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Settings feature coming soon'),
              ),
            );
          },
        ),
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            child: Column(
              children: [
                SizedBox(height: 3.h),
                _buildAvatarSection(),
                SizedBox(height: 2.h),
                _buildUserInfo(),
                SizedBox(height: 3.h),
                ProfileInfoCardWidget(userData: combinedUserData),
                SizedBox(height: 3.h),
                ProfileActionListWidget(
                  onEditProfileTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Edit Profile feature coming soon'),
                      ),
                    );
                  },
                  onNotificationSettingsTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Notification settings coming soon'),
                      ),
                    );
                  },
                  onPrivacySettingsTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Privacy settings coming soon'),
                      ),
                    );
                  },
                  onHelpSupportTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Help & Support coming soon'),
                      ),
                    );
                  },
                ),
                SizedBox(height: 4.h),
                LogoutButtonWidget(
                  onLogoutTap: _handleLogout,
                ),
                SizedBox(height: 4.h),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAvatarSection() {
    return GestureDetector(
      onTap: _handleAvatarTap,
      child: Stack(
        children: [
          Container(
            width: 25.w,
            height: 25.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppTheme.lightTheme.primaryColor,
                width: 3,
              ),
            ),
            child: ClipOval(
              child: _userData['avatar'] != null
                  ? CustomImageWidget(
                      imageUrl: _userData['avatar'] as String,
                      width: 25.w,
                      height: 25.w,
                      fit: BoxFit.cover,
                    )
                  : Container(
                      color: Colors.grey.shade300,
                      child: CustomIconWidget(
                        iconName: 'person',
                        color: Colors.grey.shade600,
                        size: 12.w,
                      ),
                    ),
            ),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              width: 8.w,
              height: 8.w,
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.primaryColor,
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppTheme.lightTheme.scaffoldBackgroundColor,
                  width: 2,
                ),
              ),
              child: CustomIconWidget(
                iconName: 'camera_alt',
                color: Colors.white,
                size: 4.w,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfo() {
    return Column(
      children: [
        Text(
          _userData['name'] as String? ?? 'Unknown User',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 0.5.h),
        Text(
          _userData['phone'] as String? ?? 'No phone number',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 0.5.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'location_on',
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              size: 4.w,
            ),
            SizedBox(width: 1.w),
            Text(
              _userData['region'] as String? ?? 'Unknown location',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  ),
            ),
          ],
        ),
      ],
    );
  }
}
