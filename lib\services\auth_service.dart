import 'package:supabase_flutter/supabase_flutter.dart';

import './supabase_service.dart';

class AuthService {
  final SupabaseService _supabaseService = SupabaseService();

  Future<SupabaseClient> get _client => _supabaseService.client;

  // Sign up with email and password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    required String fullName,
    String? phone,
    String? region,
  }) async {
    try {
      final client = await _client;
      final response = await client.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
          'phone': phone,
          'region': region,
        },
      );
      return response;
    } catch (error) {
      throw Exception('Sign-up failed: $error');
    }
  }

  // Sign in with email and password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final client = await _client;
      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (error) {
      throw Exception('Sign-in failed: $error');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      final client = await _client;
      await client.auth.signOut();
    } catch (error) {
      throw Exception('Sign-out failed: $error');
    }
  }

  // Get current user
  User? getCurrentUser() {
    try {
      final client = _supabaseService.syncClient;
      return client.auth.currentUser;
    } catch (error) {
      return null;
    }
  }

  // Check if user is authenticated
  bool isAuthenticated() {
    return getCurrentUser() != null;
  }

  // Listen to auth state changes
  Stream<AuthState> get authStateChanges {
    return _supabaseService.syncClient.auth.onAuthStateChange;
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      final client = await _client;
      await client.auth.resetPasswordForEmail(email);
    } catch (error) {
      throw Exception('Password reset failed: $error');
    }
  }
}
