import 'package:supabase_flutter/supabase_flutter.dart';

import './supabase_service.dart';

class GroupService {
  final SupabaseService _supabaseService = SupabaseService();

  Future<SupabaseClient> get _client => _supabaseService.client;

  // Fetch user's joined groups
  Future<List<Map<String, dynamic>>> fetchUserGroups() async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await client.from('group_members').select('''
            *,
            group:groups!group_id(
              *,
              created_by:user_profiles!created_by(id, full_name)
            )
          ''').eq('user_id', user.id).order('joined_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch user groups: $error');
    }
  }

  // Create a new group
  Future<Map<String, dynamic>> createGroup({
    required String name,
    required String description,
    String privacy = 'public',
    String? avatarUrl,
  }) async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Create group
      final groupResponse = await client
          .from('groups')
          .insert({
            'name': name,
            'description': description,
            'privacy': privacy,
            'avatar_url': avatarUrl,
            'created_by': user.id,
          })
          .select()
          .single();

      // Add creator as admin member
      await client.from('group_members').insert({
        'group_id': groupResponse['id'],
        'user_id': user.id,
        'role': 'admin',
      });

      return groupResponse;
    } catch (error) {
      throw Exception('Failed to create group: $error');
    }
  }

  // Join a group
  Future<void> joinGroup(String groupId) async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      await client.from('group_members').insert({
        'group_id': groupId,
        'user_id': user.id,
        'role': 'member',
      });
    } catch (error) {
      throw Exception('Failed to join group: $error');
    }
  }

  // Leave a group
  Future<void> leaveGroup(String groupId) async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      await client
          .from('group_members')
          .delete()
          .eq('group_id', groupId)
          .eq('user_id', user.id);
    } catch (error) {
      throw Exception('Failed to leave group: $error');
    }
  }

  // Get group details
  Future<Map<String, dynamic>> getGroupById(String groupId) async {
    try {
      final client = await _client;
      final response = await client.from('groups').select('''
            *,
            created_by:user_profiles!created_by(id, full_name, avatar_url),
            members:group_members(
              *,
              user:user_profiles!user_id(id, full_name, avatar_url)
            ),
            queries(*)
          ''').eq('id', groupId).single();

      return response;
    } catch (error) {
      throw Exception('Failed to fetch group: $error');
    }
  }

  // Update group
  Future<Map<String, dynamic>> updateGroup({
    required String groupId,
    String? name,
    String? description,
    String? privacy,
    String? avatarUrl,
  }) async {
    try {
      final client = await _client;
      final updateData = <String, dynamic>{};

      if (name != null) updateData['name'] = name;
      if (description != null) updateData['description'] = description;
      if (privacy != null) updateData['privacy'] = privacy;
      if (avatarUrl != null) updateData['avatar_url'] = avatarUrl;

      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await client
          .from('groups')
          .update(updateData)
          .eq('id', groupId)
          .select()
          .single();

      return response;
    } catch (error) {
      throw Exception('Failed to update group: $error');
    }
  }

  // Delete group
  Future<void> deleteGroup(String groupId) async {
    try {
      final client = await _client;
      await client.from('groups').delete().eq('id', groupId);
    } catch (error) {
      throw Exception('Failed to delete group: $error');
    }
  }

  // Search public groups
  Future<List<Map<String, dynamic>>> searchPublicGroups(
      String searchTerm) async {
    try {
      final client = await _client;
      final response = await client
          .from('groups')
          .select('''
            *,
            created_by:user_profiles!created_by(id, full_name)
          ''')
          .eq('privacy', 'public')
          .or('name.ilike.%$searchTerm%,description.ilike.%$searchTerm%')
          .order('member_count', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to search groups: $error');
    }
  }

  // Get group members
  Future<List<Map<String, dynamic>>> getGroupMembers(String groupId) async {
    try {
      final client = await _client;
      final response = await client.from('group_members').select('''
            *,
            user:user_profiles!user_id(id, full_name, avatar_url)
          ''').eq('group_id', groupId).order('joined_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch group members: $error');
    }
  }
}
