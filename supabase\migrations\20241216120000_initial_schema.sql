-- <PERSON>reate custom types
CREATE TYPE public.user_role AS <PERSON>NUM ('admin', 'moderator', 'member');
CREATE TYPE public.query_status AS ENUM ('open', 'answered', 'closed');
CREATE TYPE public.group_privacy AS ENUM ('public', 'private');

-- Create user_profiles table (intermediary for auth.users)
CREATE TABLE public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    phone TEXT,
    region TEXT,
    avatar_url TEXT,
    role public.user_role DEFAULT 'member'::public.user_role,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create groups table
CREATE TABLE public.groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    avatar_url TEXT,
    privacy public.group_privacy DEFAULT 'public'::public.group_privacy,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    member_count INTEGER DEFAULT 1,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create group_members table
CREATE TABLE public.group_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID REFERENCES public.groups(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role TEXT DEFAULT 'member',
    joined_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(group_id, user_id)
);

-- Create queries table
CREATE TABLE public.queries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    author_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    group_id UUID REFERENCES public.groups(id) ON DELETE SET NULL,
    status public.query_status DEFAULT 'open'::public.query_status,
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create query_files table
CREATE TABLE public.query_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_id UUID REFERENCES public.queries(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_size BIGINT,
    file_url TEXT NOT NULL,
    uploaded_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create comments table
CREATE TABLE public.comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_id UUID REFERENCES public.queries(id) ON DELETE CASCADE,
    author_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    likes_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create likes table
CREATE TABLE public.likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    query_id UUID REFERENCES public.queries(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, query_id)
);

-- Create indexes
CREATE INDEX idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX idx_groups_created_by ON public.groups(created_by);
CREATE INDEX idx_group_members_group_id ON public.group_members(group_id);
CREATE INDEX idx_group_members_user_id ON public.group_members(user_id);
CREATE INDEX idx_queries_author_id ON public.queries(author_id);
CREATE INDEX idx_queries_group_id ON public.queries(group_id);
CREATE INDEX idx_queries_created_at ON public.queries(created_at DESC);
CREATE INDEX idx_query_files_query_id ON public.query_files(query_id);
CREATE INDEX idx_comments_query_id ON public.comments(query_id);
CREATE INDEX idx_comments_author_id ON public.comments(author_id);
CREATE INDEX idx_likes_query_id ON public.likes(query_id);
CREATE INDEX idx_likes_user_id ON public.likes(user_id);

-- Enable RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.queries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.query_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.likes ENABLE ROW LEVEL SECURITY;

-- Helper functions for RLS policies
CREATE OR REPLACE FUNCTION public.is_group_member(group_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.group_members gm
    WHERE gm.group_id = group_uuid AND gm.user_id = auth.uid()
)
$$;

CREATE OR REPLACE FUNCTION public.can_access_query(query_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.queries q
    LEFT JOIN public.groups g ON q.group_id = g.id
    WHERE q.id = query_uuid 
    AND (
        q.group_id IS NULL OR
        g.privacy = 'public'::public.group_privacy OR
        public.is_group_member(q.group_id)
    )
)
$$;

-- RLS Policies
CREATE POLICY "users_own_profile" ON public.user_profiles
FOR ALL TO authenticated
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

CREATE POLICY "public_can_read_profiles" ON public.user_profiles
FOR SELECT TO public
USING (true);

CREATE POLICY "users_can_read_public_groups" ON public.groups
FOR SELECT TO authenticated
USING (privacy = 'public'::public.group_privacy OR public.is_group_member(id));

CREATE POLICY "users_can_create_groups" ON public.groups
FOR INSERT TO authenticated
WITH CHECK (auth.uid() = created_by);

CREATE POLICY "group_creators_can_update" ON public.groups
FOR UPDATE TO authenticated
USING (auth.uid() = created_by)
WITH CHECK (auth.uid() = created_by);

CREATE POLICY "members_can_view_memberships" ON public.group_members
FOR SELECT TO authenticated
USING (public.is_group_member(group_id));

CREATE POLICY "users_can_join_groups" ON public.group_members
FOR INSERT TO authenticated
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "users_can_leave_groups" ON public.group_members
FOR DELETE TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "public_can_read_queries" ON public.queries
FOR SELECT TO authenticated
USING (public.can_access_query(id));

CREATE POLICY "authenticated_can_create_queries" ON public.queries
FOR INSERT TO authenticated
WITH CHECK (auth.uid() = author_id);

CREATE POLICY "authors_can_update_queries" ON public.queries
FOR UPDATE TO authenticated
USING (auth.uid() = author_id)
WITH CHECK (auth.uid() = author_id);

CREATE POLICY "query_files_access" ON public.query_files
FOR SELECT TO authenticated
USING (public.can_access_query(query_id));

CREATE POLICY "authors_can_manage_files" ON public.query_files
FOR ALL TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.queries q
        WHERE q.id = query_id AND q.author_id = auth.uid()
    )
);

CREATE POLICY "public_can_read_comments" ON public.comments
FOR SELECT TO authenticated
USING (public.can_access_query(query_id));

CREATE POLICY "authenticated_can_create_comments" ON public.comments
FOR INSERT TO authenticated
WITH CHECK (auth.uid() = author_id AND public.can_access_query(query_id));

CREATE POLICY "authors_can_update_comments" ON public.comments
FOR UPDATE TO authenticated
USING (auth.uid() = author_id)
WITH CHECK (auth.uid() = author_id);

CREATE POLICY "users_can_manage_likes" ON public.likes
FOR ALL TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Functions for automatic profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, full_name, role)
  VALUES (
    NEW.id, 
    NEW.email, 
    COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
    COALESCE((NEW.raw_user_meta_data->>'role')::public.user_role, 'member'::public.user_role)
  );  
  RETURN NEW;
END;
$$;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update group member count
CREATE OR REPLACE FUNCTION public.update_group_member_count()
RETURNS TRIGGER
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.groups 
        SET member_count = member_count + 1 
        WHERE id = NEW.group_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.groups 
        SET member_count = member_count - 1 
        WHERE id = OLD.group_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$;

-- Trigger for group member count
CREATE TRIGGER group_member_count_trigger
    AFTER INSERT OR DELETE ON public.group_members
    FOR EACH ROW EXECUTE FUNCTION public.update_group_member_count();

-- Mock data
DO $$
DECLARE
    admin_uuid UUID := gen_random_uuid();
    user1_uuid UUID := gen_random_uuid();
    user2_uuid UUID := gen_random_uuid();
    group1_uuid UUID := gen_random_uuid();
    group2_uuid UUID := gen_random_uuid();
    query1_uuid UUID := gen_random_uuid();
    query2_uuid UUID := gen_random_uuid();
BEGIN
    -- Create auth users with required fields
    INSERT INTO auth.users (
        id, instance_id, aud, role, email, encrypted_password, email_confirmed_at,
        created_at, updated_at, raw_user_meta_data, raw_app_meta_data,
        is_sso_user, is_anonymous, confirmation_token, confirmation_sent_at,
        recovery_token, recovery_sent_at, email_change_token_new, email_change,
        email_change_sent_at, email_change_token_current, email_change_confirm_status,
        reauthentication_token, reauthentication_sent_at, phone, phone_change,
        phone_change_token, phone_change_sent_at
    ) VALUES
        (admin_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('password123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Admin User", "phone": "******-0101", "region": "North America"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (user1_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('password123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Sarah Johnson", "phone": "******-0102", "region": "North America"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (user2_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('password123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Mike Chen", "phone": "******-0103", "region": "Asia"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null);

    -- Create groups
    INSERT INTO public.groups (id, name, description, created_by, member_count) VALUES
        (group1_uuid, 'Flutter Developers', 'A community for Flutter enthusiasts and developers', admin_uuid, 3),
        (group2_uuid, 'Mobile UI/UX Design', 'Sharing mobile design patterns and best practices', user1_uuid, 2);

    -- Create group memberships
    INSERT INTO public.group_members (group_id, user_id, role) VALUES
        (group1_uuid, admin_uuid, 'admin'),
        (group1_uuid, user1_uuid, 'member'),
        (group1_uuid, user2_uuid, 'member'),
        (group2_uuid, user1_uuid, 'admin'),
        (group2_uuid, user2_uuid, 'member');

    -- Create queries
    INSERT INTO public.queries (id, title, description, author_id, group_id, likes_count, comments_count) VALUES
        (query1_uuid, 'Best practices for Flutter state management in large applications?', 
         'I am working on a large-scale Flutter application and struggling with state management. We are currently using Provider, but I am wondering if we should migrate to Riverpod or Bloc. What are your experiences with different state management solutions?', 
         user1_uuid, group1_uuid, 24, 8),
        (query2_uuid, 'Looking for feedback on my latest UI design project', 
         'I have been working on a new mobile app design for a fitness tracking application. The design focuses on minimalism and user-friendly navigation. I would appreciate any feedback from the community.',
         user2_uuid, group2_uuid, 42, 15);

    -- Create query files
    INSERT INTO public.query_files (query_id, file_name, file_size, file_url) VALUES
        (query1_uuid, 'flutter_architecture.pdf', 2457600, 'https://example.com/files/flutter_architecture.pdf'),
        (query2_uuid, 'fitness_app_design.sketch', 16467968, 'https://example.com/files/fitness_app_design.sketch');

    -- Create comments
    INSERT INTO public.comments (query_id, author_id, content, likes_count) VALUES
        (query1_uuid, admin_uuid, 'Great question! I have used both Provider and Riverpod in production apps. Riverpod definitely has better compile-time safety.', 5),
        (query1_uuid, user2_uuid, 'Have you considered Bloc? It has excellent documentation and testing support.', 3),
        (query2_uuid, user1_uuid, 'The design looks amazing! I love the color scheme and the clean layout.', 8),
        (query2_uuid, admin_uuid, 'Very nice work! The navigation flow seems intuitive. Have you done any user testing?', 4);

    -- Create likes
    INSERT INTO public.likes (user_id, query_id) VALUES
        (admin_uuid, query1_uuid),
        (user2_uuid, query1_uuid),
        (user1_uuid, query2_uuid),
        (admin_uuid, query2_uuid);

END $$;