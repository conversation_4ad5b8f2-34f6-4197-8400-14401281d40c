import 'package:supabase_flutter/supabase_flutter.dart';

import './supabase_service.dart';

class QueryService {
  final SupabaseService _supabaseService = SupabaseService();

  Future<SupabaseClient> get _client => _supabaseService.client;

  // Fetch all queries with author and group info
  Future<List<Map<String, dynamic>>> fetchQueries(
      {int? limit, int? offset}) async {
    try {
      final client = await _client;
      var query = client.from('queries').select('''
            *,
            author:user_profiles!author_id(id, full_name, avatar_url),
            group:groups!group_id(id, name),
            query_files(*)
          ''').order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }
      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 10) - 1);
      }

      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch queries: $error');
    }
  }

  // Create a new query
  Future<Map<String, dynamic>> createQuery({
    required String title,
    required String description,
    String? groupId,
    List<Map<String, dynamic>>? files,
  }) async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Insert query
      final queryResponse = await client
          .from('queries')
          .insert({
            'title': title,
            'description': description,
            'author_id': user.id,
            'group_id': groupId,
          })
          .select()
          .single();

      // Insert files if any
      if (files != null && files.isNotEmpty) {
        final filesData = files
            .map((file) => {
                  'query_id': queryResponse['id'],
                  'file_name': file['name'],
                  'file_size': file['size'],
                  'file_url': file['url'],
                })
            .toList();

        await client.from('query_files').insert(filesData);
      }

      return queryResponse;
    } catch (error) {
      throw Exception('Failed to create query: $error');
    }
  }

  // Update query
  Future<Map<String, dynamic>> updateQuery({
    required String queryId,
    String? title,
    String? description,
    String? status,
  }) async {
    try {
      final client = await _client;
      final updateData = <String, dynamic>{};

      if (title != null) updateData['title'] = title;
      if (description != null) updateData['description'] = description;
      if (status != null) updateData['status'] = status;

      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await client
          .from('queries')
          .update(updateData)
          .eq('id', queryId)
          .select()
          .single();

      return response;
    } catch (error) {
      throw Exception('Failed to update query: $error');
    }
  }

  // Delete query
  Future<void> deleteQuery(String queryId) async {
    try {
      final client = await _client;
      await client.from('queries').delete().eq('id', queryId);
    } catch (error) {
      throw Exception('Failed to delete query: $error');
    }
  }

  // Get query by ID with details
  Future<Map<String, dynamic>> getQueryById(String queryId) async {
    try {
      final client = await _client;
      final response = await client.from('queries').select('''
            *,
            author:user_profiles!author_id(id, full_name, avatar_url),
            group:groups!group_id(id, name),
            query_files(*),
            comments(
              *,
              author:user_profiles!author_id(id, full_name, avatar_url)
            )
          ''').eq('id', queryId).single();

      return response;
    } catch (error) {
      throw Exception('Failed to fetch query: $error');
    }
  }

  // Like/Unlike query
  Future<void> toggleLike(String queryId) async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Check if already liked
      final existingLike = await client
          .from('likes')
          .select()
          .eq('user_id', user.id)
          .eq('query_id', queryId)
          .maybeSingle();

      if (existingLike != null) {
        // Unlike
        await client
            .from('likes')
            .delete()
            .eq('user_id', user.id)
            .eq('query_id', queryId);

        await client
            .rpc('decrement_likes_count', params: {'query_uuid': queryId});
      } else {
        // Like
        await client.from('likes').insert({
          'user_id': user.id,
          'query_id': queryId,
        });

        await client
            .rpc('increment_likes_count', params: {'query_uuid': queryId});
      }
    } catch (error) {
      throw Exception('Failed to toggle like: $error');
    }
  }

  // Add comment to query
  Future<Map<String, dynamic>> addComment({
    required String queryId,
    required String content,
  }) async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await client.from('comments').insert({
        'query_id': queryId,
        'author_id': user.id,
        'content': content,
      }).select('''
            *,
            author:user_profiles!author_id(id, full_name, avatar_url)
          ''').single();

      // Increment comments count
      await client
          .rpc('increment_comments_count', params: {'query_uuid': queryId});

      return response;
    } catch (error) {
      throw Exception('Failed to add comment: $error');
    }
  }

  // Search queries
  Future<List<Map<String, dynamic>>> searchQueries(String searchTerm) async {
    try {
      final client = await _client;
      final response = await client
          .from('queries')
          .select('''
            *,
            author:user_profiles!author_id(id, full_name, avatar_url),
            group:groups!group_id(id, name),
            query_files(*)
          ''')
          .or('title.ilike.%$searchTerm%,description.ilike.%$searchTerm%')
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to search queries: $error');
    }
  }

  // Fetch comments for a query
  Future<List<Map<String, dynamic>>> fetchCommentsForQuery(
      String queryId) async {
    try {
      final client = await _client;
      final response = await client
          .from('comments')
          .select('''
            *,
            author:user_profiles!author_id(id, full_name, avatar_url)
          ''')
          .eq('query_id', queryId)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to fetch comments: $error');
    }
  }
}
