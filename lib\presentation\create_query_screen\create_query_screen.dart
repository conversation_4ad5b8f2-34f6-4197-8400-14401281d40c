import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../services/query_service.dart';

class CreateQueryScreen extends StatefulWidget {
  const CreateQueryScreen({super.key});

  @override
  State<CreateQueryScreen> createState() => _CreateQueryScreenState();
}

class _CreateQueryScreenState extends State<CreateQueryScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _descriptionFocusNode = FocusNode();

  final QueryService _queryService = QueryService();
  final List<PlatformFile> _selectedFiles = [];
  bool _isLoading = false;
  bool _isUploading = false;
  double _uploadProgress = 0.0;

  // Character limits
  static const int _titleLimit = 100;
  static const int _descriptionLimit = 500;

  @override
  void initState() {
    super.initState();
    _titleController.addListener(_updatePostButtonState);
    _descriptionController.addListener(_updatePostButtonState);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _titleFocusNode.dispose();
    _descriptionFocusNode.dispose();
    super.dispose();
  }

  void _updatePostButtonState() {
    setState(() {});
  }

  bool get _canPost {
    return _titleController.text.trim().isNotEmpty &&
        _descriptionController.text.trim().isNotEmpty &&
        !_isLoading &&
        !_isUploading;
  }

  bool get _hasContent {
    return _titleController.text.trim().isNotEmpty ||
        _descriptionController.text.trim().isNotEmpty ||
        _selectedFiles.isNotEmpty;
  }

  Future<void> _pickFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.any,
      );

      if (result != null) {
        setState(() {
          _selectedFiles.addAll(result.files);
        });

        // Show success feedback
        HapticFeedback.lightImpact();
        Fluttertoast.showToast(
          msg: "${result.files.length} file(s) selected",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } catch (e) {
      _showErrorDialog("Failed to pick files",
          "Please try again or check file permissions.");
    }
  }

  void _removeFile(int index) {
    setState(() {
      _selectedFiles.removeAt(index);
    });
    HapticFeedback.lightImpact();
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  Future<void> _submitQuery() async {
    if (!_canPost) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate file upload progress if files are selected
      if (_selectedFiles.isNotEmpty) {
        setState(() {
          _isUploading = true;
          _uploadProgress = 0.0;
        });

        // Simulate upload progress
        for (int i = 0; i <= 100; i += 10) {
          await Future.delayed(const Duration(milliseconds: 100));
          setState(() {
            _uploadProgress = i / 100;
          });
        }
      }

      // Prepare file data for Supabase
      List<Map<String, dynamic>>? filesData;
      if (_selectedFiles.isNotEmpty) {
        filesData = _selectedFiles
            .map((file) => {
                  'name': file.name,
                  'size': file.size,
                  'url':
                      'https://example.com/files/${file.name}', // TODO: Upload to Supabase Storage
                })
            .toList();
      }

      // Create query in Supabase
      await _queryService.createQuery(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        files: filesData,
      );

      // Success feedback
      HapticFeedback.mediumImpact();
      Fluttertoast.showToast(
        msg: "Query posted successfully!",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );

      // Navigate back to home
      // ignore: use_build_context_synchronously
      Navigator.of(context).pop();
    } catch (e) {
      _showErrorDialog("Upload Failed",
          "Failed to post query: ${e.toString()}. Please check your connection and try again.");
    } finally {
      setState(() {
        _isLoading = false;
        _isUploading = false;
        _uploadProgress = 0.0;
      });
    }
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
            if (title == "Upload Failed")
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _submitQuery();
                },
                child: const Text('Retry'),
              ),
          ],
        );
      },
    );
  }

  Future<bool> _onWillPop() async {
    if (!_hasContent) return true;

    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Discard Query?'),
              content: const Text(
                  'You have unsaved changes. Are you sure you want to discard this query?'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Discard'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: AppTheme.lightTheme.appBarTheme.backgroundColor,
          elevation: AppTheme.lightTheme.appBarTheme.elevation,
          leading: TextButton(
            onPressed: () async {
              if (await _onWillPop()) {
                // ignore: use_build_context_synchronously
                Navigator.of(context).pop();
              }
            },
            child: Text(
              'Cancel',
              style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                color: AppTheme.lightTheme.colorScheme.primary,
              ),
            ),
          ),
          title: Text(
            'New Query',
            style: AppTheme.lightTheme.appBarTheme.titleTextStyle,
          ),
          centerTitle: true,
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 4.w),
              child: TextButton(
                onPressed: _canPost ? _submitQuery : null,
                child: _isLoading
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppTheme.lightTheme.colorScheme.primary,
                          ),
                        ),
                      )
                    : Text(
                        'Post',
                        style:
                            AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                          color: _canPost
                              ? AppTheme.lightTheme.colorScheme.primary
                              : AppTheme
                                  .lightTheme.colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
        body: SafeArea(
          child: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title Input Section
                  _buildTitleSection(),

                  SizedBox(height: 3.h),

                  // Description Input Section
                  _buildDescriptionSection(),

                  SizedBox(height: 3.h),

                  // File Attachment Section
                  _buildFileAttachmentSection(),

                  // Upload Progress Section
                  if (_isUploading) ...[
                    SizedBox(height: 2.h),
                    _buildUploadProgressSection(),
                  ],

                  SizedBox(height: 4.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Query Title',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _titleFocusNode.hasFocus
                  ? AppTheme.lightTheme.colorScheme.primary
                  : AppTheme.lightTheme.colorScheme.outline,
              width: _titleFocusNode.hasFocus ? 2 : 1,
            ),
          ),
          child: TextField(
            controller: _titleController,
            focusNode: _titleFocusNode,
            maxLength: _titleLimit,
            maxLines: 1,
            enabled: !_isLoading && !_isUploading,
            style: AppTheme.lightTheme.textTheme.bodyLarge,
            decoration: InputDecoration(
              hintText: 'What\'s your question?',
              hintStyle: AppTheme.lightTheme.inputDecorationTheme.hintStyle,
              border: InputBorder.none,
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.5.h),
              counterText: '',
            ),
            onChanged: (value) {
              if (value.length > _titleLimit) {
                _titleController.text = value.substring(0, _titleLimit);
                _titleController.selection = TextSelection.fromPosition(
                  TextPosition(offset: _titleLimit),
                );
              }
            },
          ),
        ),
        SizedBox(height: 0.5.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              '${_titleController.text.length}/$_titleLimit',
              style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                color: _titleController.text.length > _titleLimit * 0.9
                    ? AppTheme.lightTheme.colorScheme.error
                    : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _descriptionFocusNode.hasFocus
                  ? AppTheme.lightTheme.colorScheme.primary
                  : AppTheme.lightTheme.colorScheme.outline,
              width: _descriptionFocusNode.hasFocus ? 2 : 1,
            ),
          ),
          child: TextField(
            controller: _descriptionController,
            focusNode: _descriptionFocusNode,
            maxLength: _descriptionLimit,
            maxLines: null,
            minLines: 4,
            enabled: !_isLoading && !_isUploading,
            style: AppTheme.lightTheme.textTheme.bodyLarge,
            decoration: InputDecoration(
              hintText: 'Provide more details about your query...',
              hintStyle: AppTheme.lightTheme.inputDecorationTheme.hintStyle,
              border: InputBorder.none,
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.5.h),
              counterText: '',
            ),
            onChanged: (value) {
              if (value.length > _descriptionLimit) {
                _descriptionController.text =
                    value.substring(0, _descriptionLimit);
                _descriptionController.selection = TextSelection.fromPosition(
                  TextPosition(offset: _descriptionLimit),
                );
              }
            },
          ),
        ),
        SizedBox(height: 0.5.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              '${_descriptionController.text.length}/$_descriptionLimit',
              style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                color:
                    _descriptionController.text.length > _descriptionLimit * 0.9
                        ? AppTheme.lightTheme.colorScheme.error
                        : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFileAttachmentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Attachments',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),

        // Add Files Button
        InkWell(
          onTap: (_isLoading || _isUploading) ? null : _pickFiles,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.lightTheme.colorScheme.outline,
                style: BorderStyle.solid,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomIconWidget(
                  iconName: 'attach_file',
                  color: AppTheme.lightTheme.colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Add Files',
                  style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Selected Files List
        if (_selectedFiles.isNotEmpty) ...[
          SizedBox(height: 2.h),
          ...List.generate(_selectedFiles.length, (index) {
            final file = _selectedFiles[index];
            return Container(
              margin: EdgeInsets.only(bottom: 1.h),
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  CustomIconWidget(
                    iconName: 'insert_drive_file',
                    color: AppTheme.lightTheme.colorScheme.onPrimaryContainer,
                    size: 16,
                  ),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          file.name,
                          style: AppTheme.lightTheme.textTheme.bodyMedium
                              ?.copyWith(
                            color: AppTheme
                                .lightTheme.colorScheme.onPrimaryContainer,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          _formatFileSize(file.size),
                          style:
                              AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme
                                .lightTheme.colorScheme.onPrimaryContainer
                                .withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (!_isLoading && !_isUploading)
                    InkWell(
                      onTap: () => _removeFile(index),
                      borderRadius: BorderRadius.circular(16),
                      child: Padding(
                        padding: EdgeInsets.all(1.w),
                        child: CustomIconWidget(
                          iconName: 'close',
                          color: AppTheme
                              .lightTheme.colorScheme.onPrimaryContainer,
                          size: 16,
                        ),
                      ),
                    ),
                ],
              ),
            );
          }),
        ],
      ],
    );
  }

  Widget _buildUploadProgressSection() {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Uploading files...',
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${(_uploadProgress * 100).toInt()}%',
                style: AppTheme.lightTheme.textTheme.bodySmall,
              ),
            ],
          ),
          SizedBox(height: 1.h),
          LinearProgressIndicator(
            value: _uploadProgress,
            backgroundColor:
                AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.3),
            valueColor: AlwaysStoppedAnimation<Color>(
              AppTheme.lightTheme.colorScheme.primary,
            ),
          ),
          SizedBox(height: 1.h),
          TextButton(
            onPressed: () {
              setState(() {
                _isUploading = false;
                _uploadProgress = 0.0;
              });
            },
            child: Text(
              'Cancel Upload',
              style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                color: AppTheme.lightTheme.colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
