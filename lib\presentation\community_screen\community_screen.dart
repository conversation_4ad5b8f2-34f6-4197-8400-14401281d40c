import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../services/group_service.dart';
import './widgets/create_group_modal_widget.dart';
import './widgets/group_card_widget.dart';

class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen> {
  final TextEditingController _searchController = TextEditingController();
  final GroupService _groupService = GroupService();

  bool _isLoading = false;
  bool _isSearching = false;
  String _searchQuery = '';
  List<Map<String, dynamic>> _joinedGroups = [];

  @override
  void initState() {
    super.initState();
    _loadUserGroups();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadUserGroups() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final groups = await _groupService.fetchUserGroups();
      setState(() {
        _joinedGroups = groups.map((membership) {
          final group = membership['group'];
          return {
            'id': group['id'],
            'name': group['name'],
            'description': group['description'],
            'avatar': group['avatar_url'],
            'memberCount': group['member_count'],
            'lastActivity':
                _formatTimeAgo(group['updated_at'] ?? group['created_at']),
            'unreadCount': 0, // TODO: Implement real-time messaging
            'isPinned': false, // TODO: Implement pinning feature
            'isMuted': false, // TODO: Implement muting feature
            'lastMessage': 'Group created',
            'role': membership['role'],
            'joinedAt': membership['joined_at'],
          };
        }).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load groups: ${e.toString()}'),
            backgroundColor: AppTheme.lightTheme.colorScheme.error,
          ),
        );
      }
    }
  }

  String _formatTimeAgo(String? dateTimeString) {
    if (dateTimeString == null) return 'Unknown';

    try {
      final dateTime = DateTime.parse(dateTimeString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays > 0) {
        return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return 'Unknown';
    }
  }

  List<Map<String, dynamic>> get _filteredGroups {
    if (_searchQuery.isEmpty) {
      // Sort by pinned first, then by last activity
      final sortedGroups = List<Map<String, dynamic>>.from(_joinedGroups);
      sortedGroups.sort((a, b) {
        if ((a['isPinned'] as bool) && !(b['isPinned'] as bool)) return -1;
        if (!(a['isPinned'] as bool) && (b['isPinned'] as bool)) return 1;
        return 0;
      });
      return sortedGroups;
    }

    return _joinedGroups.where((group) {
      final name = (group['name'] as String).toLowerCase();
      final description = (group['description'] as String? ?? '').toLowerCase();
      final query = _searchQuery.toLowerCase();
      return name.contains(query) || description.contains(query);
    }).toList();
  }

  Future<void> _refreshGroups() async {
    await _loadUserGroups();
  }

  void _showCreateGroupModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateGroupModalWidget(
        onGroupCreated: (groupData) async {
          try {
            await _groupService.createGroup(
              name: groupData['name'],
              description: groupData['description'],
            );

            // ignore: use_build_context_synchronously
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    Text('Group "${groupData['name']}" created successfully!'),
                backgroundColor: AppTheme.lightTheme.colorScheme.tertiary,
              ),
            );

            // Refresh the groups list
            _loadUserGroups();
          } catch (e) {
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to create group: ${e.toString()}'),
                backgroundColor: AppTheme.lightTheme.colorScheme.error,
              ),
            );
          }
        },
      ),
    );
  }

  void _onGroupTap(Map<String, dynamic> group) {
    // Navigate to group detail screen - TODO: Implement group detail screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${group['name']} group'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _onGroupLongPress(Map<String, dynamic> group) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.symmetric(vertical: 2.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 10.w,
              height: 0.5.h,
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.outline,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            SizedBox(height: 2.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'info',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text('View Info'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Viewing ${group['name']} info')),
                );
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: group['isMuted'] ? 'volume_up' : 'volume_off',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text(group['isMuted'] ? 'Unmute' : 'Mute'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  group['isMuted'] = !group['isMuted'];
                });
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: group['isPinned'] ? 'push_pin' : 'push_pin',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text(group['isPinned'] ? 'Unpin' : 'Pin'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  group['isPinned'] = !group['isPinned'];
                });
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'exit_to_app',
                color: AppTheme.lightTheme.colorScheme.error,
                size: 24,
              ),
              title: Text(
                'Leave Group',
                style: TextStyle(color: AppTheme.lightTheme.colorScheme.error),
              ),
              onTap: () {
                Navigator.pop(context);
                _showLeaveGroupDialog(group);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showLeaveGroupDialog(Map<String, dynamic> group) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Group'),
        content: Text('Are you sure you want to leave "${group['name']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              try {
                await _groupService.leaveGroup(group['id']);
                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Left ${group['name']}'),
                    backgroundColor: AppTheme.lightTheme.colorScheme.error,
                  ),
                );

                // Refresh the groups list
                _loadUserGroups();
              } catch (e) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to leave group: ${e.toString()}'),
                    backgroundColor: AppTheme.lightTheme.colorScheme.error,
                  ),
                );
              }
            },
            child: Text(
              'Leave',
              style: TextStyle(color: AppTheme.lightTheme.colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.scaffoldBackgroundColor,
                border: Border(
                  bottom: BorderSide(
                    color: AppTheme.lightTheme.colorScheme.outline
                        .withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'My Groups',
                      style:
                          AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _isSearching = !_isSearching;
                        if (!_isSearching) {
                          _searchController.clear();
                          _searchQuery = '';
                        }
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: AppTheme.lightTheme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: CustomIconWidget(
                        iconName: _isSearching ? 'close' : 'search',
                        color: AppTheme.lightTheme.colorScheme.primary,
                        size: 24,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Search Bar
            if (_isSearching)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                child: TextField(
                  controller: _searchController,
                  autofocus: true,
                  decoration: const InputDecoration(
                    hintText: 'Search groups...',
                    prefixIcon: Icon(Icons.search),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),

            // Content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredGroups.isEmpty
                      ? _buildEmptyState()
                      : RefreshIndicator(
                          onRefresh: _refreshGroups,
                          child: ListView.builder(
                            padding: EdgeInsets.symmetric(vertical: 1.h),
                            itemCount: _filteredGroups.length,
                            itemBuilder: (context, index) {
                              final group = _filteredGroups[index];
                              return GroupCardWidget(
                                group: group,
                                onTap: () => _onGroupTap(group),
                                onLongPress: () => _onGroupLongPress(group),
                              );
                            },
                          ),
                        ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateGroupModal,
        child: CustomIconWidget(
          iconName: 'add',
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomImageWidget(
              imageUrl:
                  "https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=300&h=300&fit=crop",
              width: 40.w,
              height: 40.w,
              fit: BoxFit.cover,
            ),
            SizedBox(height: 3.h),
            Text(
              'No Groups Yet',
              style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Join your first group to start connecting with like-minded people',
              textAlign: TextAlign.center,
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              ),
            ),
            SizedBox(height: 4.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _showCreateGroupModal,
                child: const Text('Create Group'),
              ),
            ),
            SizedBox(height: 2.h),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('Browse Groups feature coming soon!')),
                  );
                },
                child: const Text('Browse Groups'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
