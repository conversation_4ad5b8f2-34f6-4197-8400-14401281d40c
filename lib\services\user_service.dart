import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:io';

import './supabase_service.dart';

class UserService {
  final SupabaseService _supabaseService = SupabaseService();

  Future<SupabaseClient> get _client => _supabaseService.client;

  // Get current user profile
  Future<Map<String, dynamic>?> getCurrentUserProfile() async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;
      
      if (user == null) {
        return null;
      }

      final response = await client
          .from('user_profiles')
          .select()
          .eq('id', user.id)
          .maybeSingle();

      return response;
    } catch (error) {
      throw Exception('Failed to fetch user profile: $error');
    }
  }

  // Update user profile
  Future<Map<String, dynamic>> updateUserProfile({
    String? fullName,
    String? phone,
    String? region,
    String? avatarUrl,
  }) async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;
      
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final updateData = <String, dynamic>{};
      
      if (fullName != null) updateData['full_name'] = fullName;
      if (phone != null) updateData['phone'] = phone;
      if (region != null) updateData['region'] = region;
      if (avatarUrl != null) updateData['avatar_url'] = avatarUrl;
      
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await client
          .from('user_profiles')
          .update(updateData)
          .eq('id', user.id)
          .select()
          .single();

      return response;
    } catch (error) {
      throw Exception('Failed to update user profile: $error');
    }
  }

  // Get user by ID
  Future<Map<String, dynamic>?> getUserById(String userId) async {
    try {
      final client = await _client;
      final response = await client
          .from('user_profiles')
          .select()
          .eq('id', userId)
          .maybeSingle();

      return response;
    } catch (error) {
      throw Exception('Failed to fetch user: $error');
    }
  }

  // Get user statistics
  Future<Map<String, dynamic>> getUserStatistics() async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get user's query count
      final queriesResponse = await client
          .from('queries')
          .select('id')
          .eq('author_id', user.id);
      final queriesCount = queriesResponse.length;

      // Get user's groups count
      final groupsResponse = await client
          .from('group_members')
          .select('user_id')
          .eq('user_id', user.id);
      final groupsCount = groupsResponse.length;

      // Get user's comments count
      final commentsResponse = await client
          .from('comments')
          .select('id')
          .eq('author_id', user.id);
      final commentsCount = commentsResponse.length;

      // Get user's likes received count
      final likesResponse = await client
          .from('likes')
          .select('*, query:queries!inner(author_id)')
          .eq('query.author_id', user.id);
      final likesReceivedCount = likesResponse.length;

      return {
        'queries_count': queriesCount,
        'groups_count': groupsCount,
        'comments_count': commentsCount,
        'likes_received': likesReceivedCount,
      };
    } catch (error) {
      throw Exception('Failed to fetch user statistics: $error');
    }
  }

  // Upload profile avatar
  Future<String> uploadAvatar(String filePath, String fileName) async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;
      
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Upload file to storage
      final uploadPath = 'avatars/${user.id}/$fileName';
      await client.storage.from('avatars').upload(uploadPath, File(filePath));

      // Get public URL
      final publicUrl = client.storage.from('avatars').getPublicUrl(uploadPath);

      // Update user profile with new avatar URL
      await updateUserProfile(avatarUrl: publicUrl);

      return publicUrl;
    } catch (error) {
      throw Exception('Failed to upload avatar: $error');
    }
  }

  // Delete user account
  Future<void> deleteUserAccount() async {
    try {
      final client = await _client;
      final user = client.auth.currentUser;
      
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Delete user profile (cascades to related data)
      await client.from('user_profiles').delete().eq('id', user.id);

      // Sign out
      await client.auth.signOut();
    } catch (error) {
      throw Exception('Failed to delete account: $error');
    }
  }

  // Search users
  Future<List<Map<String, dynamic>>> searchUsers(String searchTerm) async {
    try {
      final client = await _client;
      final response = await client
          .from('user_profiles')
          .select('id, full_name, avatar_url')
          .ilike('full_name', '%$searchTerm%')
          .limit(20);

      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      throw Exception('Failed to search users: $error');
    }
  }
}