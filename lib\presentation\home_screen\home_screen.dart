import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../services/query_service.dart';
import './widgets/post_card_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScrollController _scrollController = ScrollController();
  final QueryService _queryService = QueryService();

  bool _isLoading = false;
  bool _isRefreshing = false;
  bool _hasMore = true;
  int _currentPage = 0;
  static const int _pageSize = 10;
  List<Map<String, dynamic>> _posts = [];

  @override
  void initState() {
    super.initState();
    _loadInitialPosts();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadInitialPosts() async {
    await _loadPosts(refresh: true);
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMore) {
        _loadMorePosts();
      }
    }
  }

  Future<void> _loadPosts({bool refresh = false}) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      if (refresh) {
        _isRefreshing = true;
        _currentPage = 0;
        _posts.clear();
        _hasMore = true;
      }
    });

    try {
      final queries = await _queryService.fetchQueries(
        limit: _pageSize,
        offset: _currentPage * _pageSize,
      );

      setState(() {
        if (refresh) {
          _posts = queries;
        } else {
          _posts.addAll(queries);
        }

        _hasMore = queries.length == _pageSize;
        _currentPage++;
        _isLoading = false;
        _isRefreshing = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isRefreshing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load posts: ${e.toString()}'),
            backgroundColor: AppTheme.lightTheme.colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _loadMorePosts() async {
    await _loadPosts();
  }

  Future<void> _onRefresh() async {
    await _loadPosts(refresh: true);
  }

  void _onPostTap(Map<String, dynamic> post) {
    Navigator.pushNamed(context, '/post-detail-screen', arguments: post['id']);
  }

  void _onCreateQuery() {
    Navigator.pushNamed(context, '/create-query-screen');
  }

  void _onLikePost(Map<String, dynamic> post) async {
    final postIndex = _posts.indexWhere((p) => p['id'] == post['id']);
    if (postIndex == -1) return;

    final originalPost = Map<String, dynamic>.from(_posts[postIndex]);
    final isLiked = originalPost['is_liked'] ?? false;
    final likesCount = originalPost['likes_count'] ?? 0;

    // Optimistic update
    setState(() {
      _posts[postIndex]['is_liked'] = !isLiked;
      _posts[postIndex]['likes_count'] = likesCount + (!isLiked ? 1 : -1);
    });

    try {
      await _queryService.toggleLike(post['id']);
    } catch (e) {
      // Revert on error
      setState(() {
        _posts[postIndex] = originalPost;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to like post: ${e.toString()}'),
            backgroundColor: AppTheme.lightTheme.colorScheme.error,
          ),
        );
      }
    }
  }

  Widget _buildShimmerCard() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 10.w,
                height: 10.w,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 30.w,
                      height: 2.h,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    SizedBox(height: 0.5.h),
                    Container(
                      width: 20.w,
                      height: 1.5.h,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Container(
            width: double.infinity,
            height: 2.5.h,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          SizedBox(height: 1.h),
          Container(
            width: 80.w,
            height: 2.h,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(8.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'forum',
              size: 20.w,
              color: AppTheme.lightTheme.colorScheme.primary
                  .withValues(alpha: 0.5),
            ),
            SizedBox(height: 3.h),
            Text(
              'No posts yet',
              style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Be the first to share a query with the community',
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            ElevatedButton.icon(
              onPressed: _onCreateQuery,
              icon: CustomIconWidget(
                iconName: 'add',
                size: 5.w,
                color: Colors.white,
              ),
              label: const Text('Create your first query'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.5.h),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: Row(
          children: [
            CustomIconWidget(
              iconName: 'forum',
              size: 7.w,
              color: AppTheme.lightTheme.colorScheme.primary,
            ),
            SizedBox(width: 2.w),
            Text(
              'Community Hub',
              style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.lightTheme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Notifications opened'),
                  duration: Duration(seconds: 1),
                ),
              );
            },
            icon: CustomIconWidget(
              iconName: 'notifications_outlined',
              size: 6.w,
              color: AppTheme.lightTheme.colorScheme.onSurface,
            ),
          ),
          SizedBox(width: 2.w),
        ],
      ),
      body: _posts.isEmpty && !_isRefreshing
          ? _buildEmptyState()
          : RefreshIndicator(
              onRefresh: _onRefresh,
              color: AppTheme.lightTheme.colorScheme.primary,
              child: ListView.builder(
                controller: _scrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.only(
                  top: 1.h,
                  bottom: 10.h,
                ),
                itemCount: _posts.length + (_isLoading ? 2 : 0),
                itemBuilder: (context, index) {
                  if (index >= _posts.length) {
                    return _buildShimmerCard();
                  }

                  final post = _posts[index];
                  return PostCardWidget(
                    post: post,
                    onTap: () => _onPostTap(post),
                    onSave: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Post saved: ${post["title"]}'),
                          duration: const Duration(seconds: 1),
                        ),
                      );
                    },
                    onShare: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Sharing post: ${post["title"]}'),
                          duration: const Duration(seconds: 1),
                        ),
                      );
                    },
                    onReport: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Post reported: ${post["title"]}'),
                          duration: const Duration(seconds: 1),
                        ),
                      );
                    },
                    onLike: () => _onLikePost(post),
                  );
                },
              ),
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _onCreateQuery,
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
        foregroundColor: Colors.white,
        icon: CustomIconWidget(
          iconName: 'add',
          size: 6.w,
          color: Colors.white,
        ),
        label: Text(
          'Create Query',
          style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }
}
