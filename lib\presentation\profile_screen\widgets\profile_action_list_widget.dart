import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ProfileActionListWidget extends StatelessWidget {
  final VoidCallback onEditProfileTap;
  final VoidCallback onNotificationSettingsTap;
  final VoidCallback onPrivacySettingsTap;
  final VoidCallback onHelpSupportTap;

  const ProfileActionListWidget({
    super.key,
    required this.onEditProfileTap,
    required this.onNotificationSettingsTap,
    required this.onPrivacySettingsTap,
    required this.onHelpSupportTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildActionItem(
            context,
            'Edit Profile',
            'edit',
            onEditProfileTap,
            showDivider: true,
          ),
          _buildActionItem(
            context,
            'Notification Settings',
            'notifications',
            onNotificationSettingsTap,
            showDivider: true,
          ),
          _buildActionItem(
            context,
            'Privacy Settings',
            'privacy_tip',
            onPrivacySettingsTap,
            showDivider: true,
          ),
          _buildActionItem(
            context,
            'Help & Support',
            'help',
            onHelpSupportTap,
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildActionItem(
    BuildContext context,
    String title,
    String iconName,
    VoidCallback onTap, {
    bool showDivider = true,
  }) {
    return Column(
      children: [
        ListTile(
          contentPadding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
          leading: Container(
            width: 10.w,
            height: 10.w,
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: CustomIconWidget(
              iconName: iconName,
              color: AppTheme.lightTheme.primaryColor,
              size: 5.w,
            ),
          ),
          title: Text(
            title,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
          trailing: CustomIconWidget(
            iconName: 'chevron_right',
            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            size: 5.w,
          ),
          onTap: onTap,
        ),
        showDivider
            ? Divider(
                height: 1,
                indent: 4.w,
                endIndent: 4.w,
                color: AppTheme.lightTheme.colorScheme.outline
                    .withValues(alpha: 0.2),
              )
            : const SizedBox.shrink(),
      ],
    );
  }
}
