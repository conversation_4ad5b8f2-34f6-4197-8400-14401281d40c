import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/app_export.dart';

class PostCardWidget extends StatelessWidget {
  final Map<String, dynamic> post;
  final VoidCallback onTap;
  final VoidCallback onSave;
  final VoidCallback onShare;
  final VoidCallback onReport;
  final VoidCallback? onLike;

  const PostCardWidget({
    super.key,
    required this.post,
    required this.onTap,
    required this.onSave,
    required this.onShare,
    required this.onReport,
    this.onLike,
  });

  @override
  Widget build(BuildContext context) {
    // Extract data from Supabase format
    final author = post['author'] is List && (post['author'] as List).isNotEmpty
        ? (post['author'] as List).first
        : post['author'] ?? {};
    final files = post['query_files'] as List? ?? [];
    final hasFiles = files.isNotEmpty;

    final authorName = author['full_name'] ?? 'Unknown User';
    final authorAvatar = author['avatar_url'] ?? '';
    final title = post['title'] ?? '';
    final description = post['description'] ?? '';
    final likesCount = post['likes_count'] ?? 0;
    final commentsCount = post['comments_count'] ?? 0;
    final createdAt = post['created_at'] != null
        ? DateTime.parse(post['created_at'])
        : DateTime.now();

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with user info and menu
              Row(
                children: [
                  // User Avatar
                  Container(
                    width: 10.w,
                    height: 10.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppTheme.lightTheme.colorScheme.outline
                            .withValues(alpha: 0.3),
                      ),
                    ),
                    child: ClipOval(
                      child: authorAvatar.isNotEmpty
                          ? CustomImageWidget(
                              imageUrl: authorAvatar,
                              width: 10.w,
                              height: 10.w,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              color: AppTheme.lightTheme.colorScheme.primary
                                  .withValues(alpha: 0.1),
                              child: CustomIconWidget(
                                iconName: 'person',
                                color: AppTheme.lightTheme.colorScheme.primary,
                                size: 5.w,
                              ),
                            ),
                    ),
                  ),

                  SizedBox(width: 3.w),

                  // User Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          authorName,
                          style: AppTheme.lightTheme.textTheme.bodyMedium
                              ?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 0.25.h),
                        Text(
                          timeago.format(createdAt),
                          style:
                              AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme
                                .lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Menu Button
                  PopupMenuButton(
                    icon: CustomIconWidget(
                      iconName: 'more_vert',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 5.w,
                    ),
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        onTap: onSave,
                        child: Row(
                          children: [
                            CustomIconWidget(
                              iconName: 'bookmark_border',
                              color: AppTheme.lightTheme.colorScheme.onSurface,
                              size: 20,
                            ),
                            SizedBox(width: 3.w),
                            const Text('Save'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        onTap: onShare,
                        child: Row(
                          children: [
                            CustomIconWidget(
                              iconName: 'share',
                              color: AppTheme.lightTheme.colorScheme.onSurface,
                              size: 20,
                            ),
                            SizedBox(width: 3.w),
                            const Text('Share'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        onTap: onReport,
                        child: Row(
                          children: [
                            CustomIconWidget(
                              iconName: 'flag',
                              color: AppTheme.lightTheme.colorScheme.error,
                              size: 20,
                            ),
                            SizedBox(width: 3.w),
                            Text(
                              'Report',
                              style: TextStyle(
                                color: AppTheme.lightTheme.colorScheme.error,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 3.h),

              // Post Title
              Text(
                title,
                style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.lightTheme.colorScheme.onSurface,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: 1.5.h),

              // Post Description
              Text(
                description,
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              // File Attachment (if exists)
              if (hasFiles) ...[
                SizedBox(height: 2.h),
                Container(
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    color: AppTheme.lightTheme.colorScheme.primaryContainer
                        .withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.lightTheme.colorScheme.outline
                          .withValues(alpha: 0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      CustomIconWidget(
                        iconName: 'attach_file',
                        color: AppTheme.lightTheme.colorScheme.primary,
                        size: 5.w,
                      ),
                      SizedBox(width: 2.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              files.first['file_name'] ?? 'Unknown file',
                              style: AppTheme.lightTheme.textTheme.bodySmall
                                  ?.copyWith(
                                fontWeight: FontWeight.w500,
                                color:
                                    AppTheme.lightTheme.colorScheme.onSurface,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (files.first['file_size'] != null)
                              Text(
                                _formatFileSize(files.first['file_size']),
                                style: AppTheme.lightTheme.textTheme.bodySmall
                                    ?.copyWith(
                                  color: AppTheme
                                      .lightTheme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              SizedBox(height: 3.h),

              // Action Buttons
              Row(
                children: [
                  // Like Button
                  _buildActionButton(
                    icon: 'favorite_border',
                    label: likesCount.toString(),
                    onTap: onLike,
                  ),

                  SizedBox(width: 6.w),

                  // Comment Button
                  _buildActionButton(
                    icon: 'comment',
                    label: commentsCount.toString(),
                    onTap: onTap,
                  ),

                  const Spacer(),

                  // Group indicator (if exists)
                  if (post['group'] != null) ...[
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 2.w,
                        vertical: 0.5.h,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.lightTheme.colorScheme.secondary
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CustomIconWidget(
                            iconName: 'groups',
                            color: AppTheme.lightTheme.colorScheme.secondary,
                            size: 3.w,
                          ),
                          SizedBox(width: 1.w),
                          Text(
                            post['group']['name'] ?? '',
                            style: AppTheme.lightTheme.textTheme.bodySmall
                                ?.copyWith(
                              color: AppTheme.lightTheme.colorScheme.secondary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String icon,
    required String label,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomIconWidget(
              iconName: icon,
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              size: 4.w,
            ),
            SizedBox(width: 1.w),
            Text(
              label,
              style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
