import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../services/query_service.dart';
import './widgets/comment_card_widget.dart';

class PostDetailScreen extends StatefulWidget {
  final String postId;

  const PostDetailScreen({super.key, required this.postId});

  @override
  State<PostDetailScreen> createState() => _PostDetailScreenState();
}

class _PostDetailScreenState extends State<PostDetailScreen> {
  final QueryService _queryService = QueryService();
  final TextEditingController _commentController = TextEditingController();

  bool _isLoading = true;
  Map<String, dynamic>? _post;
  List<Map<String, dynamic>> _comments = [];

  @override
  void initState() {
    super.initState();
    _loadPostDetails();
  }

  Future<void> _loadPostDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final post = await _queryService.getQueryById(widget.postId);
      final comments = await _queryService.fetchCommentsForQuery(widget.postId);

      setState(() {
        _post = post;
        _comments = comments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load post details: \${e.toString()}'),
            backgroundColor: AppTheme.lightTheme.colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _postComment() async {
    if (_commentController.text.trim().isEmpty) {
      return;
    }

    try {
      final newComment = await _queryService.addComment(
        queryId: widget.postId,
        content: _commentController.text.trim(),
      );

      setState(() {
        _comments.insert(0, newComment);
        _commentController.clear();
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to post comment: \${e.toString()}'),
            backgroundColor: AppTheme.lightTheme.colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isLoading ? 'Loading...' : _post?['title'] ?? 'Post Details'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _post == null
              ? const Center(child: Text('Post not found'))
              : Column(
                  children: [
                    Expanded(
                      child: RefreshIndicator(
                        onRefresh: _loadPostDetails,
                        child: SingleChildScrollView(
                          padding: EdgeInsets.all(4.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _post!['title'],
                                style: AppTheme.lightTheme.textTheme.headlineSmall,
                              ),
                              SizedBox(height: 2.h),
                              Text(
                                _post!['content'],
                                style: AppTheme.lightTheme.textTheme.bodyLarge,
                              ),
                              SizedBox(height: 4.h),
                              Text(
                                'Comments (\${_comments.length})',
                                style: AppTheme.lightTheme.textTheme.titleMedium,
                              ),
                              SizedBox(height: 2.h),
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: _comments.length,
                                itemBuilder: (context, index) {
                                  return CommentCardWidget(
                                    comment: _comments[index],
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    _buildCommentInputField(),
                  ],
                ),
    );
  }

  Widget _buildCommentInputField() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: const InputDecoration(
                hintText: 'Add a comment...',
                border: InputBorder.none,
              ),
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _postComment(),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.send),
            onPressed: _postComment,
            color: AppTheme.lightTheme.colorScheme.primary,
          ),
        ],
      ),
    );
  }
}
