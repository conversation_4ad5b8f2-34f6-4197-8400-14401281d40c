import 'package:flutter/material.dart';

import '../presentation/create_query_screen/create_query_screen.dart';
import '../presentation/home_screen/home_screen.dart';
import '../presentation/post_detail_screen/post_detail_screen.dart';
import '../presentation/profile_screen/profile_screen.dart';
import '../presentation/registration_screen/registration_screen.dart';
import '../presentation/splash_screen/splash_screen.dart';

class AppRoutes {
  static const String homeScreen = '/home-screen';
  static const String splashScreen = '/splash-screen';
  static const String registrationScreen = '/registration-screen';
  static const String createQueryScreen = '/create-query-screen';
  static const String profileScreen = '/profile-screen';
  static const String postDetailScreen = '/post-detail-screen';

  static Map<String, WidgetBuilder> routes = {
    homeScreen: (context) => const HomeScreen(),
    splashScreen: (context) => const SplashScreen(),
    registrationScreen: (context) => const RegistrationScreen(),
    createQueryScreen: (context) => const CreateQueryScreen(),
    profileScreen: (context) => const ProfileScreen(),
    postDetailScreen: (context) {
      final postId = ModalRoute.of(context)!.settings.arguments as String;
      return PostDetailScreen(postId: postId);
    },
  };
}
