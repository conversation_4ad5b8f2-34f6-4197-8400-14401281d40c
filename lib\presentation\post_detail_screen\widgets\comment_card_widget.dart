import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/app_export.dart';

class CommentCardWidget extends StatelessWidget {
  final Map<String, dynamic> comment;

  const CommentCardWidget({super.key, required this.comment});

  @override
  Widget build(BuildContext context) {
    final author = comment['author'] as Map<String, dynamic>? ?? {};
    final authorName = author['full_name'] as String? ?? 'Anonymous';
    final authorAvatar = author['avatar_url'] as String?;
    final createdAt = DateTime.parse(comment['created_at']);

    return Card(
      margin: EdgeInsets.symmetric(vertical: 1.h),
      child: Padding(
        padding: EdgeInsets.all(3.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 5.w,
                  backgroundImage:
                      authorAvatar != null ? NetworkImage(authorAvatar) : null,
                  child: authorAvatar == null
                      ? Icon(Icons.person, size: 6.w)
                      : null,
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        authorName,
                        style: AppTheme.lightTheme.textTheme.titleSmall,
                      ),
                      Text(
                        timeago.format(createdAt),
                        style: AppTheme.lightTheme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            Text(
              comment['content'],
              style: AppTheme.lightTheme.textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
